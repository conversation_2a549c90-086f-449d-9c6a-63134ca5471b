<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\BusinessCreationValidationService;
use App\Services\EnterpriseIsolationService;
use App\Models\Business;
use App\Models\User;

class AuditBusinessCreationSystem extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'audit:business-creation 
                            {--business-id= : Specific business ID to audit}
                            {--all : Audit all businesses}
                            {--isolation : Run enterprise isolation audit}
                            {--detailed : Show detailed output}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Audit the business creation system for completeness and enterprise isolation';

    protected $businessValidationService;
    protected $isolationService;

    public function __construct(
        BusinessCreationValidationService $businessValidationService,
        EnterpriseIsolationService $isolationService
    ) {
        parent::__construct();
        $this->businessValidationService = $businessValidationService;
        $this->isolationService = $isolationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 Starting Business Creation System Audit...');
        $this->newLine();

        $results = [
            'timestamp' => now()->toISOString(),
            'audits' => [],
            'summary' => [
                'total_businesses' => 0,
                'passed_businesses' => 0,
                'failed_businesses' => 0,
                'isolation_status' => 'NOT_RUN'
            ]
        ];

        // Run enterprise isolation audit if requested
        if ($this->option('isolation')) {
            $this->info('🔒 Running Enterprise Isolation Audit...');
            $isolationResults = $this->isolationService->runIsolationAudit();
            $results['isolation_audit'] = $isolationResults;
            $results['summary']['isolation_status'] = $isolationResults['summary']['failed_checks'] === 0 ? 'PASSED' : 'FAILED';
            
            $this->displayIsolationResults($isolationResults);
            $this->newLine();
        }

        // Determine which businesses to audit
        $businesses = $this->getBusinessesToAudit();
        
        if ($businesses->isEmpty()) {
            $this->warn('No businesses found to audit.');
            return 0;
        }

        $this->info("📊 Auditing {$businesses->count()} business(es)...");
        $this->newLine();

        $progressBar = $this->output->createProgressBar($businesses->count());
        $progressBar->start();

        foreach ($businesses as $business) {
            $auditResult = $this->businessValidationService->validateBusinessCreation($business->id);
            $results['audits'][] = $auditResult;
            
            if ($auditResult['status'] === 'PASSED') {
                $results['summary']['passed_businesses']++;
            } else {
                $results['summary']['failed_businesses']++;
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine(2);

        $results['summary']['total_businesses'] = $businesses->count();

        // Display results
        $this->displayAuditResults($results);

        // Save detailed results if requested
        if ($this->option('detailed')) {
            $this->saveDetailedResults($results);
        }

        return $results['summary']['failed_businesses'] === 0 ? 0 : 1;
    }

    /**
     * Get businesses to audit based on options
     */
    private function getBusinessesToAudit()
    {
        if ($businessId = $this->option('business-id')) {
            return Business::where('id', $businessId)->get();
        }

        if ($this->option('all')) {
            return Business::with(['owner', 'landingPage', 'landingServiceSettings', 'seoSettings'])->get();
        }

        // Default: audit recent businesses (last 10)
        return Business::with(['owner', 'landingPage', 'landingServiceSettings', 'seoSettings'])
                      ->latest()
                      ->limit(10)
                      ->get();
    }

    /**
     * Display isolation audit results
     */
    private function displayIsolationResults($results)
    {
        $this->table(
            ['Check Name', 'Status', 'Details'],
            collect($results['checks'])->map(function ($check) {
                return [
                    $check['name'],
                    $this->formatStatus($check['status']),
                    $check['details']
                ];
            })->toArray()
        );

        $summary = $results['summary'];
        $this->info("✅ Passed: {$summary['passed_checks']}");
        $this->info("❌ Failed: {$summary['failed_checks']}");
        $this->info("⚠️  Violations: {$summary['violations_found']}");
    }

    /**
     * Display audit results
     */
    private function displayAuditResults($results)
    {
        $this->info('📋 Audit Summary:');
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Businesses', $results['summary']['total_businesses']],
                ['Passed Audits', $results['summary']['passed_businesses']],
                ['Failed Audits', $results['summary']['failed_businesses']],
                ['Isolation Status', $this->formatStatus($results['summary']['isolation_status'])]
            ]
        );

        // Show failed businesses
        $failedAudits = collect($results['audits'])->where('status', '!=', 'PASSED');
        
        if ($failedAudits->isNotEmpty()) {
            $this->newLine();
            $this->error('❌ Failed Business Audits:');
            
            foreach ($failedAudits as $audit) {
                $this->line("• {$audit['business_name']} (ID: {$audit['business_id']}) - {$audit['message']}");
                
                if ($this->option('detailed')) {
                    foreach ($audit['validations'] as $category => $validation) {
                        if ($validation['status'] === 'FAIL') {
                            $this->line("  └─ {$category}: {$validation['message']}");
                        }
                    }
                }
            }
        } else {
            $this->newLine();
            $this->info('✅ All business audits passed!');
        }

        // Overall system status
        $this->newLine();
        $overallStatus = $results['summary']['failed_businesses'] === 0 && 
                        $results['summary']['isolation_status'] !== 'FAILED';
        
        if ($overallStatus) {
            $this->info('🎉 Business Creation System: FULLY OPERATIONAL');
        } else {
            $this->error('⚠️  Business Creation System: ISSUES DETECTED');
        }
    }

    /**
     * Format status with colors
     */
    private function formatStatus($status)
    {
        switch ($status) {
            case 'PASS':
            case 'PASSED':
                return "<fg=green>{$status}</>";
            case 'FAIL':
            case 'FAILED':
                return "<fg=red>{$status}</>";
            case 'WARNING':
                return "<fg=yellow>{$status}</>";
            case 'NOT_RUN':
                return "<fg=gray>{$status}</>";
            default:
                return $status;
        }
    }

    /**
     * Save detailed results to file
     */
    private function saveDetailedResults($results)
    {
        $filename = 'business_creation_audit_' . now()->format('Y-m-d_H-i-s') . '.json';
        $filepath = storage_path("logs/{$filename}");
        
        file_put_contents($filepath, json_encode($results, JSON_PRETTY_PRINT));
        
        $this->info("📄 Detailed results saved to: {$filepath}");
    }
}
