<?php $__env->startSection('title', 'Services'); ?>

<?php $__env->startSection('content_header'); ?>
    <div class="row">
        <div class="col-sm-6">
            <h1>Services</h1>
            <p class="text-muted">Manage your business services and offerings</p>
        </div>
        <div class="col-sm-6">
            <div class="float-right">
                <a href="<?php echo e(route('owner.service-categories.index')); ?>" class="btn btn-info">
                    <i class="fas fa-tags mr-2"></i>
                    Categories
                </a>
                <a href="<?php echo e(route('owner.services.create')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    Add Service
                </a>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle mr-2"></i>
            <?php echo e(session('success')); ?>

            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle mr-2"></i>
            <?php echo e(session('error')); ?>

            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            <strong>Please fix the following errors:</strong>
            <ul class="mb-0 mt-2">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
        </div>
    <?php endif; ?>

    
    <div class="row mb-3">
        <div class="col-lg-3 col-6">
            <div class="small-box bg-info">
                <div class="inner">
                    <h3><?php echo e($stats['total_services']); ?></h3>
                    <p>Total Services</p>
                </div>
                <div class="icon">
                    <i class="fas fa-concierge-bell"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-success">
                <div class="inner">
                    <h3><?php echo e($stats['active_services']); ?></h3>
                    <p>Active Services</p>
                </div>
                <div class="icon">
                    <i class="fas fa-check-circle"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-warning">
                <div class="inner">
                    <h3>$<?php echo e(number_format($stats['average_price'], 2)); ?></h3>
                    <p>Average Price</p>
                </div>
                <div class="icon">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-6">
            <div class="small-box bg-primary">
                <div class="inner">
                    <h3><?php echo e($stats['total_categories']); ?></h3>
                    <p>Categories</p>
                </div>
                <div class="icon">
                    <i class="fas fa-tags"></i>
                </div>
            </div>
        </div>
    </div>

    
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-filter mr-2"></i>
                Filters
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <form method="GET">
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="search">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?php echo e(request('search')); ?>"
                                   placeholder="Service name or description">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="category">Category</label>
                            <select class="form-control" id="category" name="category">
                                <option value="">All Categories</option>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($category->id); ?>" <?php echo e(request('category') == $category->id ? 'selected' : ''); ?>>
                                        <?php echo e($category->name); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="status">Status</label>
                            <select class="form-control" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="active" <?php echo e(request('status') == 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="inactive" <?php echo e(request('status') == 'inactive' ? 'selected' : ''); ?>>Inactive</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="price_range">Price Range</label>
                            <select class="form-control" id="price_range" name="price_range">
                                <option value="">All Prices</option>
                                <option value="under_50" <?php echo e(request('price_range') == 'under_50' ? 'selected' : ''); ?>>Under $50</option>
                                <option value="50_100" <?php echo e(request('price_range') == '50_100' ? 'selected' : ''); ?>>$50 - $100</option>
                                <option value="100_200" <?php echo e(request('price_range') == '100_200' ? 'selected' : ''); ?>>$100 - $200</option>
                                <option value="over_200" <?php echo e(request('price_range') == 'over_200' ? 'selected' : ''); ?>>Over $200</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="duration">Duration</label>
                            <select class="form-control" id="duration" name="duration">
                                <option value="">All Durations</option>
                                <option value="under_30" <?php echo e(request('duration') == 'under_30' ? 'selected' : ''); ?>>Under 30 min</option>
                                <option value="30_60" <?php echo e(request('duration') == '30_60' ? 'selected' : ''); ?>>30-60 min</option>
                                <option value="60_120" <?php echo e(request('duration') == '60_120' ? 'selected' : ''); ?>>1-2 hours</option>
                                <option value="over_120" <?php echo e(request('duration') == 'over_120' ? 'selected' : ''); ?>>Over 2 hours</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-1">
                        <div class="form-group">
                            <label>&nbsp;</label>
                            <div>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    
    <div class="card">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-concierge-bell mr-2"></i>
                Services List
            </h3>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Service</th>
                            <th>Category</th>
                            <th>Duration</th>
                            <th>Price</th>
                            <th>Bookings</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="service-icon <?php echo e($service->category ? 'bg-primary' : 'bg-secondary'); ?> text-white mr-3"
                                             style="width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                            <i class="<?php echo e($service->category->icon ?? 'fas fa-concierge-bell'); ?>"></i>
                                        </div>
                                        <div>
                                            <strong><?php echo e($service->name); ?></strong>
                                            <?php if($service->short_description): ?>
                                                <br><small class="text-muted"><?php echo e(Str::limit($service->short_description, 50)); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <?php if($service->category): ?>
                                        <span class="badge badge-info"><?php echo e($service->category->name); ?></span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">No Category</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo e($service->duration_minutes); ?> min</strong>
                                    <br><small class="text-muted">
                                        <?php if($service->duration_minutes < 60): ?>
                                            Quick service
                                        <?php elseif($service->duration_minutes <= 120): ?>
                                            Standard duration
                                        <?php else: ?>
                                            Extended service
                                        <?php endif; ?>
                                    </small>
                                </td>
                                <td>
                                    <strong>$<?php echo e(number_format($service->base_price, 2)); ?></strong>
                                    <br><small class="text-muted">Base price</small>
                                    <?php if($service->deposit_required): ?>
                                        <br><small class="text-warning">
                                            <i class="fas fa-exclamation-triangle"></i> Deposit required
                                        </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong><?php echo e($service->booking_services_count ?? 0); ?></strong>
                                    <br><small class="text-muted">total bookings</small>
                                </td>
                                <td>
                                    <?php if($service->is_active): ?>
                                        <span class="badge badge-success">Active</span>
                                    <?php else: ?>
                                        <span class="badge badge-secondary">Inactive</span>
                                    <?php endif; ?>

                                    <?php if($service->requires_approval): ?>
                                        <br><span class="badge badge-warning">Requires Approval</span>
                                    <?php endif; ?>

                                    <?php if(!$service->online_booking_enabled): ?>
                                        <br><span class="badge badge-info">In-person Only</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-group-vertical" role="group">
                                        <a href="<?php echo e(route('owner.services.show', $service)); ?>" class="btn btn-sm btn-info" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo e(route('owner.services.edit', $service)); ?>" class="btn btn-sm btn-warning" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <form action="<?php echo e(route('owner.services.duplicate', $service)); ?>" method="POST" style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-sm btn-secondary" title="Duplicate">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                        </form>
                                        <form action="<?php echo e(route('owner.services.toggle-status', $service)); ?>" method="POST" style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <button type="submit" class="btn btn-sm <?php echo e($service->is_active ? 'btn-outline-secondary' : 'btn-outline-success'); ?>"
                                                    title="<?php echo e($service->is_active ? 'Deactivate' : 'Activate'); ?>">
                                                <i class="fas <?php echo e($service->is_active ? 'fa-pause' : 'fa-play'); ?>"></i>
                                            </button>
                                        </form>
                                        <form action="<?php echo e(route('owner.services.destroy', $service)); ?>" method="POST"
                                              style="display: inline;"
                                              onsubmit="return confirm('Are you sure you want to delete this service? This action cannot be undone.')">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-sm btn-danger" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-concierge-bell fa-3x mb-3"></i>
                                        <h5>No services found</h5>
                                        <p>You haven't created any services yet, or no services match your current filters.</p>
                                        <a href="<?php echo e(route('owner.services.create')); ?>" class="btn btn-primary">
                                            <i class="fas fa-plus mr-2"></i>
                                            Create Your First Service
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">
                <div class="col-sm-12 col-md-5">
                    <div class="dataTables_info">
                        Showing <?php echo e($services->firstItem() ?? 0); ?> to <?php echo e($services->lastItem() ?? 0); ?> of <?php echo e($services->total()); ?> services
                    </div>
                </div>
                <div class="col-sm-12 col-md-7">
                    <div class="float-right">
                        <?php echo e($services->appends(request()->query())->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('owner.layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\bookkei\resources\views/owner/services/index.blade.php ENDPATH**/ ?>