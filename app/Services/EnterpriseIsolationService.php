<?php

namespace App\Services;

use App\Models\Business;
use App\Models\User;
use App\Models\OwnerNotification;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class EnterpriseIsolationService
{
    /**
     * Validate enterprise isolation for business data access
     */
    public function validateBusinessAccess($businessId, $userId = null): bool
    {
        try {
            $userId = $userId ?? Auth::id();
            
            if (!$userId) {
                Log::warning('Enterprise isolation validation failed: No authenticated user');
                return false;
            }

            $user = User::find($userId);
            if (!$user) {
                Log::warning('Enterprise isolation validation failed: User not found', ['user_id' => $userId]);
                return false;
            }

            // Super Admin and Admin can access all businesses
            if ($user->hasRole(['Super Admin', 'Admin'])) {
                return true;
            }

            // Business Owner can only access their own businesses
            if ($user->hasRole('Business Owner')) {
                $hasAccess = $user->ownedBusinesses()->where('id', $businessId)->exists();
                
                if (!$hasAccess) {
                    Log::warning('Enterprise isolation violation: Owner attempted to access unauthorized business', [
                        'user_id' => $userId,
                        'business_id' => $businessId,
                        'user_email' => $user->email,
                        'ip_address' => request()->ip(),
                        'user_agent' => request()->userAgent()
                    ]);
                }
                
                return $hasAccess;
            }

            // Staff can only access businesses they're employed by
            if ($user->hasRole(['Manager', 'Staff'])) {
                $hasAccess = $user->employedBusinesses()->where('id', $businessId)->exists();
                
                if (!$hasAccess) {
                    Log::warning('Enterprise isolation violation: Staff attempted to access unauthorized business', [
                        'user_id' => $userId,
                        'business_id' => $businessId,
                        'user_email' => $user->email,
                        'ip_address' => request()->ip(),
                        'user_agent' => request()->userAgent()
                    ]);
                }
                
                return $hasAccess;
            }

            Log::warning('Enterprise isolation validation failed: Unknown role', [
                'user_id' => $userId,
                'business_id' => $businessId,
                'user_roles' => $user->getRoleNames()
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('Enterprise isolation validation error', [
                'user_id' => $userId,
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Validate notification access with enterprise isolation
     */
    public function validateNotificationAccess($notificationId, $userId = null): bool
    {
        try {
            $userId = $userId ?? Auth::id();
            
            if (!$userId) {
                return false;
            }

            $notification = OwnerNotification::find($notificationId);
            if (!$notification) {
                Log::warning('Notification access validation failed: Notification not found', [
                    'notification_id' => $notificationId,
                    'user_id' => $userId
                ]);
                return false;
            }

            // Check if user owns the business associated with the notification
            $hasAccess = $this->validateBusinessAccess($notification->business_id, $userId);
            
            // Additional check: notification must belong to the user
            if ($hasAccess && $notification->owner_id !== $userId) {
                Log::warning('Enterprise isolation violation: User attempted to access notification not owned by them', [
                    'user_id' => $userId,
                    'notification_id' => $notificationId,
                    'notification_owner_id' => $notification->owner_id,
                    'business_id' => $notification->business_id
                ]);
                return false;
            }

            return $hasAccess;
        } catch (\Exception $e) {
            Log::error('Notification access validation error', [
                'user_id' => $userId,
                'notification_id' => $notificationId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get allowed business IDs for a user
     */
    public function getAllowedBusinessIds($userId = null): array
    {
        try {
            $userId = $userId ?? Auth::id();
            
            if (!$userId) {
                return [];
            }

            $cacheKey = "allowed_business_ids_{$userId}";
            
            return Cache::remember($cacheKey, 300, function () use ($userId) {
                $user = User::find($userId);
                
                if (!$user) {
                    return [];
                }

                // Super Admin and Admin can access all businesses
                if ($user->hasRole(['Super Admin', 'Admin'])) {
                    return Business::pluck('id')->toArray();
                }

                // Business Owner can access their own businesses
                if ($user->hasRole('Business Owner')) {
                    return $user->ownedBusinesses()->pluck('id')->toArray();
                }

                // Staff can access businesses they're employed by
                if ($user->hasRole(['Manager', 'Staff'])) {
                    return $user->employedBusinesses()->pluck('id')->toArray();
                }

                return [];
            });
        } catch (\Exception $e) {
            Log::error('Failed to get allowed business IDs', [
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * Apply enterprise isolation to query builder
     */
    public function applyBusinessIsolation($query, $businessIdColumn = 'business_id', $userId = null)
    {
        $allowedBusinessIds = $this->getAllowedBusinessIds($userId);
        
        if (empty($allowedBusinessIds)) {
            // If no allowed businesses, return empty result
            return $query->whereRaw('1 = 0');
        }

        return $query->whereIn($businessIdColumn, $allowedBusinessIds);
    }

    /**
     * Validate and log data access attempt
     */
    public function logDataAccess($resourceType, $resourceId, $action, $userId = null): void
    {
        try {
            $userId = $userId ?? Auth::id();
            
            Log::info('Data access logged', [
                'user_id' => $userId,
                'resource_type' => $resourceType,
                'resource_id' => $resourceId,
                'action' => $action,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log data access', [
                'error' => $e->getMessage(),
                'resource_type' => $resourceType,
                'resource_id' => $resourceId
            ]);
        }
    }

    /**
     * Run enterprise isolation audit
     */
    public function runIsolationAudit(): array
    {
        try {
            $auditResults = [
                'timestamp' => now()->toISOString(),
                'checks' => [],
                'violations' => [],
                'summary' => [
                    'total_checks' => 0,
                    'passed_checks' => 0,
                    'failed_checks' => 0,
                    'violations_found' => 0
                ]
            ];

            // Check 1: Verify all notifications have proper business association
            $orphanedNotifications = OwnerNotification::whereDoesntHave('business')->count();
            $auditResults['checks'][] = [
                'name' => 'Orphaned Notifications Check',
                'status' => $orphanedNotifications === 0 ? 'PASS' : 'FAIL',
                'details' => "Found {$orphanedNotifications} notifications without business association"
            ];

            // Check 2: Verify notification owner matches business owner
            $mismatchedNotifications = OwnerNotification::whereHas('business', function ($query) {
                $query->whereColumn('owner_id', '!=', 'owner_notifications.owner_id');
            })->count();
            
            $auditResults['checks'][] = [
                'name' => 'Notification Owner Mismatch Check',
                'status' => $mismatchedNotifications === 0 ? 'PASS' : 'FAIL',
                'details' => "Found {$mismatchedNotifications} notifications with owner mismatch"
            ];

            // Check 3: Verify business isolation in database constraints
            $constraintCheck = $this->verifyDatabaseConstraints();
            $auditResults['checks'][] = [
                'name' => 'Database Constraints Check',
                'status' => $constraintCheck['status'],
                'details' => $constraintCheck['details']
            ];

            // Check 4: Verify global scopes are working
            $scopeCheck = $this->verifyGlobalScopes();
            $auditResults['checks'][] = [
                'name' => 'Global Scopes Check',
                'status' => $scopeCheck['status'],
                'details' => $scopeCheck['details']
            ];

            // Calculate summary
            $auditResults['summary']['total_checks'] = count($auditResults['checks']);
            $auditResults['summary']['passed_checks'] = count(array_filter($auditResults['checks'], fn($check) => $check['status'] === 'PASS'));
            $auditResults['summary']['failed_checks'] = $auditResults['summary']['total_checks'] - $auditResults['summary']['passed_checks'];
            $auditResults['summary']['violations_found'] = $orphanedNotifications + $mismatchedNotifications;

            return $auditResults;
        } catch (\Exception $e) {
            Log::error('Enterprise isolation audit failed', ['error' => $e->getMessage()]);
            return [
                'timestamp' => now()->toISOString(),
                'error' => 'Audit failed: ' . $e->getMessage(),
                'checks' => [],
                'violations' => [],
                'summary' => ['total_checks' => 0, 'passed_checks' => 0, 'failed_checks' => 0, 'violations_found' => 0]
            ];
        }
    }

    /**
     * Verify database constraints for enterprise isolation
     */
    private function verifyDatabaseConstraints(): array
    {
        try {
            // Check if foreign key constraints exist
            $constraints = DB::select("
                SELECT CONSTRAINT_NAME, TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE REFERENCED_TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'owner_notifications'
                AND REFERENCED_TABLE_NAME IN ('businesses', 'users')
            ");

            $hasBusinessConstraint = collect($constraints)->contains('REFERENCED_TABLE_NAME', 'businesses');
            $hasOwnerConstraint = collect($constraints)->contains('REFERENCED_TABLE_NAME', 'users');

            if ($hasBusinessConstraint && $hasOwnerConstraint) {
                return ['status' => 'PASS', 'details' => 'All required foreign key constraints are in place'];
            } else {
                return ['status' => 'FAIL', 'details' => 'Missing required foreign key constraints'];
            }
        } catch (\Exception $e) {
            return ['status' => 'ERROR', 'details' => 'Could not verify constraints: ' . $e->getMessage()];
        }
    }

    /**
     * Verify global scopes are working correctly
     */
    private function verifyGlobalScopes(): array
    {
        try {
            // Test with a mock user to see if global scopes are applied
            $testUser = User::whereHas('ownedBusinesses')->first();
            
            if (!$testUser) {
                return ['status' => 'SKIP', 'details' => 'No test user with businesses found'];
            }

            // Temporarily authenticate as test user
            $originalUser = Auth::user();
            Auth::login($testUser);

            // Check if global scope limits results
            $allNotifications = OwnerNotification::withoutGlobalScopes()->count();
            $scopedNotifications = OwnerNotification::count();

            Auth::login($originalUser);

            if ($scopedNotifications <= $allNotifications) {
                return ['status' => 'PASS', 'details' => "Global scopes working: {$scopedNotifications}/{$allNotifications} notifications visible"];
            } else {
                return ['status' => 'FAIL', 'details' => 'Global scopes not working correctly'];
            }
        } catch (\Exception $e) {
            return ['status' => 'ERROR', 'details' => 'Could not verify global scopes: ' . $e->getMessage()];
        }
    }

    /**
     * Clear enterprise isolation cache
     */
    public function clearIsolationCache($userId = null): void
    {
        try {
            if ($userId) {
                Cache::forget("allowed_business_ids_{$userId}");
            } else {
                // Clear all user business cache
                $users = User::pluck('id');
                foreach ($users as $id) {
                    Cache::forget("allowed_business_ids_{$id}");
                }
            }
        } catch (\Exception $e) {
            Log::error('Failed to clear isolation cache', ['error' => $e->getMessage()]);
        }
    }
}
