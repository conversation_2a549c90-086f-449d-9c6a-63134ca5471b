import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

// Configure Laravel Echo for real-time notifications
window.Echo = new Echo({
    broadcaster: 'pusher',
    key: process.env.MIX_PUSHER_APP_KEY || 'bookkei-local-key',
    cluster: process.env.MIX_PUSHER_APP_CLUSTER || 'mt1',
    forceTLS: true,
    encrypted: true,
    auth: {
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        },
    },
});

// Real-time notification handling for owner panel
class OwnerNotificationManager {
    constructor() {
        this.userId = null;
        this.channel = null;
        this.init();
    }

    init() {
        // Get authenticated user ID from meta tag
        const userMeta = document.querySelector('meta[name="user-id"]');
        if (userMeta) {
            this.userId = userMeta.getAttribute('content');
            this.setupNotificationChannel();
        }
    }

    setupNotificationChannel() {
        if (!this.userId) return;

        // Listen to private channel for owner notifications
        this.channel = window.Echo.private(`owner-notifications.${this.userId}`);

        // Handle new notification events
        this.channel.listen('notification.created', (event) => {
            this.handleNewNotification(event.notification);
        });

        // Handle notification updates
        this.channel.listen('notification.updated', (event) => {
            this.handleNotificationUpdate(event.notification, event.action);
        });

        console.log('Real-time notification channel established for user:', this.userId);
    }

    handleNewNotification(notification) {
        console.log('New notification received:', notification);

        // Show browser notification if permission granted
        this.showBrowserNotification(notification);

        // Show in-app notification
        this.showInAppNotification(notification);

        // Update notification counter
        this.updateNotificationCounter();

        // Add to notification list if on notifications page
        this.addToNotificationList(notification);

        // Play notification sound
        this.playNotificationSound(notification.priority);
    }

    handleNotificationUpdate(notification, action) {
        console.log('Notification updated:', notification, action);

        // Update UI based on action
        if (action === 'read') {
            this.markNotificationAsRead(notification.id);
        } else if (action === 'deleted') {
            this.removeNotificationFromList(notification.id);
        }

        // Update counters
        this.updateNotificationCounter();
    }

    showBrowserNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const browserNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/favicon.ico',
                badge: '/favicon.ico',
                tag: `notification-${notification.id}`,
                requireInteraction: notification.priority === 'urgent',
                silent: notification.priority === 'low'
            });

            // Handle notification click
            browserNotification.onclick = () => {
                window.focus();
                // Navigate to notification or relevant page
                if (window.location.pathname.includes('/owner/notifications')) {
                    this.highlightNotification(notification.id);
                } else {
                    window.location.href = `/owner/notifications/${notification.id}`;
                }
                browserNotification.close();
            };

            // Auto-close after 5 seconds for non-urgent notifications
            if (notification.priority !== 'urgent') {
                setTimeout(() => browserNotification.close(), 5000);
            }
        }
    }

    showInAppNotification(notification) {
        // Use toastr if available
        if (typeof toastr !== 'undefined') {
            const toastrType = this.getToastrType(notification.priority);
            const options = {
                timeOut: notification.priority === 'urgent' ? 0 : 5000,
                extendedTimeOut: notification.priority === 'urgent' ? 0 : 2000,
                closeButton: true,
                progressBar: true,
                onclick: () => {
                    if (window.location.pathname.includes('/owner/notifications')) {
                        this.highlightNotification(notification.id);
                    } else {
                        window.location.href = `/owner/notifications/${notification.id}`;
                    }
                }
            };

            toastr[toastrType](notification.message, notification.title, options);
        } else {
            // Fallback to custom notification
            this.showCustomNotification(notification);
        }
    }

    showCustomNotification(notification) {
        const notificationHtml = `
            <div class="alert alert-${this.getAlertType(notification.priority)} alert-dismissible fade show notification-toast" 
                 style="position: fixed; top: 20px; right: 20px; z-index: 9999; min-width: 300px; cursor: pointer;"
                 data-notification-id="${notification.id}">
                <div class="d-flex align-items-center">
                    <i class="${this.getNotificationIcon(notification.notification_type)} mr-2"></i>
                    <div class="flex-grow-1">
                        <strong>${notification.title}</strong>
                        <div class="small">${notification.message}</div>
                    </div>
                    <button type="button" class="close ml-2" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            </div>
        `;

        $('body').append(notificationHtml);

        // Auto-remove after 5 seconds for non-urgent notifications
        if (notification.priority !== 'urgent') {
            setTimeout(() => {
                $(`.notification-toast[data-notification-id="${notification.id}"]`).alert('close');
            }, 5000);
        }

        // Handle click
        $(`.notification-toast[data-notification-id="${notification.id}"]`).on('click', () => {
            if (window.location.pathname.includes('/owner/notifications')) {
                this.highlightNotification(notification.id);
            } else {
                window.location.href = `/owner/notifications/${notification.id}`;
            }
        });
    }

    updateNotificationCounter() {
        // Update notification counter in header/sidebar
        $.get('/owner/notifications/unread-count')
            .done((response) => {
                if (response.success) {
                    $('.notification-counter').text(response.count);
                    $('.notification-counter').toggle(response.count > 0);
                }
            });
    }

    addToNotificationList(notification) {
        // Only add if we're on the notifications page
        if (!window.location.pathname.includes('/owner/notifications')) return;

        // Create notification row HTML
        const notificationRow = this.createNotificationRowHtml(notification);
        
        // Add to top of notifications table
        $('.table tbody').prepend(notificationRow);

        // Highlight new notification
        setTimeout(() => {
            this.highlightNotification(notification.id);
        }, 100);
    }

    createNotificationRowHtml(notification) {
        return `
            <tr class="notification-row unread table-warning" data-id="${notification.id}" data-read="false">
                <td width="30">
                    <div class="form-check">
                        <input class="form-check-input notification-checkbox" type="checkbox" value="${notification.id}">
                    </div>
                </td>
                <td width="50">
                    <div class="d-flex align-items-center">
                        <i class="${this.getNotificationIcon(notification.notification_type)} text-${this.getNotificationColor(notification.notification_type)} mr-2"></i>
                        ${notification.priority === 'urgent' || notification.priority === 'high' ? 
                            `<i class="${this.getPriorityIcon(notification.priority)} text-${this.getPriorityColor(notification.priority)}"></i>` : ''}
                    </div>
                </td>
                <td>
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="mb-1 notification-title font-weight-bold">
                                ${notification.title}
                                <span class="badge badge-primary badge-sm ml-1 new-badge">New</span>
                                ${notification.priority === 'urgent' ? '<span class="badge badge-danger badge-sm ml-1">Urgent</span>' : ''}
                                ${notification.priority === 'high' ? '<span class="badge badge-warning badge-sm ml-1">High</span>' : ''}
                            </h6>
                            <p class="mb-1 text-muted">${notification.message.substring(0, 100)}${notification.message.length > 100 ? '...' : ''}</p>
                            <small class="text-muted">
                                <i class="fas fa-clock mr-1"></i>
                                Just now
                                ${notification.source_type ? `• <span class="text-capitalize">${notification.source_type.replace('_', ' ')}</span>` : ''}
                            </small>
                        </div>
                        <div class="ml-3 notification-actions">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary btn-sm view-notification" data-id="${notification.id}" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success btn-sm mark-read" data-id="${notification.id}" title="Mark as Read">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger btn-sm delete-notification" data-id="${notification.id}" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    }

    highlightNotification(notificationId) {
        const row = $(`tr[data-id="${notificationId}"]`);
        if (row.length) {
            row.addClass('table-info');
            setTimeout(() => row.removeClass('table-info'), 3000);
            
            // Scroll to notification
            $('html, body').animate({
                scrollTop: row.offset().top - 100
            }, 500);
        }
    }

    markNotificationAsRead(notificationId) {
        const row = $(`tr[data-id="${notificationId}"]`);
        if (row.length) {
            row.removeClass('unread table-warning').addClass('read');
            row.find('.notification-title').removeClass('font-weight-bold');
            row.find('.new-badge').remove();
            row.find('.mark-read').hide();
            row.find('.mark-unread').show();
        }
    }

    removeNotificationFromList(notificationId) {
        $(`tr[data-id="${notificationId}"]`).fadeOut(300, function() {
            $(this).remove();
        });
    }

    playNotificationSound(priority) {
        // Play different sounds based on priority
        const soundFile = priority === 'urgent' ? 'urgent.mp3' : 
                         priority === 'high' ? 'high.mp3' : 'normal.mp3';
        
        try {
            const audio = new Audio(`/sounds/notifications/${soundFile}`);
            audio.volume = 0.3;
            audio.play().catch(e => console.log('Could not play notification sound:', e));
        } catch (e) {
            console.log('Notification sound not available:', e);
        }
    }

    // Helper methods
    getToastrType(priority) {
        const types = {
            'urgent': 'error',
            'high': 'warning',
            'normal': 'info',
            'low': 'info'
        };
        return types[priority] || 'info';
    }

    getAlertType(priority) {
        const types = {
            'urgent': 'danger',
            'high': 'warning',
            'normal': 'info',
            'low': 'secondary'
        };
        return types[priority] || 'info';
    }

    getNotificationIcon(type) {
        const icons = {
            'booking': 'fas fa-calendar-check',
            'cancellation': 'fas fa-calendar-times',
            'payment': 'fas fa-credit-card',
            'review': 'fas fa-star',
            'system': 'fas fa-cog',
            'marketing': 'fas fa-bullhorn',
            'alert': 'fas fa-exclamation-triangle',
            'reminder': 'fas fa-clock',
            'customer_message': 'fas fa-comment',
            'waiting_list': 'fas fa-list'
        };
        return icons[type] || 'fas fa-bell';
    }

    getNotificationColor(type) {
        const colors = {
            'booking': 'success',
            'cancellation': 'danger',
            'payment': 'info',
            'review': 'warning',
            'system': 'secondary',
            'marketing': 'primary',
            'alert': 'danger',
            'reminder': 'warning',
            'customer_message': 'info',
            'waiting_list': 'primary'
        };
        return colors[type] || 'info';
    }

    getPriorityIcon(priority) {
        const icons = {
            'urgent': 'fas fa-exclamation-circle',
            'high': 'fas fa-exclamation-triangle',
            'normal': 'fas fa-info-circle',
            'low': 'fas fa-minus-circle'
        };
        return icons[priority] || 'fas fa-info-circle';
    }

    getPriorityColor(priority) {
        const colors = {
            'urgent': 'danger',
            'high': 'warning',
            'normal': 'info',
            'low': 'secondary'
        };
        return colors[priority] || 'info';
    }

    // Request browser notification permission
    requestNotificationPermission() {
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission().then(permission => {
                console.log('Notification permission:', permission);
            });
        }
    }
}

// Initialize notification manager when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    window.ownerNotificationManager = new OwnerNotificationManager();
    
    // Request notification permission
    window.ownerNotificationManager.requestNotificationPermission();
});

export default OwnerNotificationManager;
