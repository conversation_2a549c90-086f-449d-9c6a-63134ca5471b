<?php

namespace App\Providers;

use App\Listeners\LogSuccessfulLogin;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        Login::class => [
            LogSuccessfulLogin::class,
        ],

        // Booking Events for Owner Notifications
        'App\Events\BookingCreated' => [
            'App\Listeners\CreateOwnerNotification',
        ],
        'App\Events\BookingCancelled' => [
            'App\Listeners\CreateOwnerNotification',
        ],
        'App\Events\BookingModified' => [
            'App\Listeners\CreateOwnerNotification',
        ],

        // Payment Events for Owner Notifications
        'App\Events\PaymentReceived' => [
            'App\Listeners\CreateOwnerNotification',
        ],
        'App\Events\PaymentFailed' => [
            'App\Listeners\CreateOwnerNotification',
        ],

        // Customer Events for Owner Notifications
        'App\Events\ReviewSubmitted' => [
            'App\Listeners\CreateOwnerNotification',
        ],
        'App\Events\CustomerMessage' => [
            'App\Listeners\CreateOwnerNotification',
        ],

        // Waiting List Events for Owner Notifications
        'App\Events\WaitingListAdded' => [
            'App\Listeners\CreateOwnerNotification',
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
