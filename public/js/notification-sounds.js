/**
 * Notification Sound System for Bookkei Owner Panel
 * Provides audio feedback for real-time notifications
 */

class NotificationSoundManager {
    constructor() {
        this.sounds = {
            'booking': '/sounds/notification-booking.mp3',
            'cancellation': '/sounds/notification-cancel.mp3',
            'payment': '/sounds/notification-payment.mp3',
            'review': '/sounds/notification-review.mp3',
            'system': '/sounds/notification-system.mp3',
            'marketing': '/sounds/notification-marketing.mp3',
            'alert': '/sounds/notification-alert.mp3',
            'reminder': '/sounds/notification-reminder.mp3',
            'customer_message': '/sounds/notification-message.mp3',
            'waiting_list': '/sounds/notification-waiting.mp3',
            'default': '/sounds/notification-default.mp3'
        };
        
        this.audioContext = null;
        this.soundBuffers = {};
        this.enabled = true;
        this.volume = 0.7;
        
        this.init();
    }

    async init() {
        try {
            // Initialize Web Audio API
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Load user preferences
            this.loadPreferences();
            
            // Preload default sounds
            await this.preloadSounds();
            
            console.log('Notification sound system initialized');
        } catch (error) {
            console.warn('Failed to initialize notification sound system:', error);
            this.fallbackToHTMLAudio();
        }
    }

    loadPreferences() {
        const preferences = localStorage.getItem('notification_sound_preferences');
        if (preferences) {
            const prefs = JSON.parse(preferences);
            this.enabled = prefs.enabled !== false;
            this.volume = prefs.volume || 0.7;
        }
    }

    savePreferences() {
        const preferences = {
            enabled: this.enabled,
            volume: this.volume
        };
        localStorage.setItem('notification_sound_preferences', JSON.stringify(preferences));
    }

    async preloadSounds() {
        const soundsToPreload = ['default', 'booking', 'alert', 'payment'];
        
        for (const soundType of soundsToPreload) {
            try {
                await this.loadSound(soundType);
            } catch (error) {
                console.warn(`Failed to preload sound: ${soundType}`, error);
            }
        }
    }

    async loadSound(type) {
        if (this.soundBuffers[type]) {
            return this.soundBuffers[type];
        }

        const soundUrl = this.sounds[type] || this.sounds.default;
        
        try {
            const response = await fetch(soundUrl);
            const arrayBuffer = await response.arrayBuffer();
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
            
            this.soundBuffers[type] = audioBuffer;
            return audioBuffer;
        } catch (error) {
            console.warn(`Failed to load sound: ${type}`, error);
            return null;
        }
    }

    async playSound(type, priority = 'normal') {
        if (!this.enabled) return;

        // Resume audio context if suspended (required by browser policies)
        if (this.audioContext && this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }

        try {
            let audioBuffer = this.soundBuffers[type];
            
            if (!audioBuffer) {
                audioBuffer = await this.loadSound(type);
            }

            if (!audioBuffer) {
                // Fallback to default sound
                audioBuffer = await this.loadSound('default');
            }

            if (audioBuffer) {
                this.playAudioBuffer(audioBuffer, priority);
            } else {
                this.fallbackSound(type, priority);
            }
        } catch (error) {
            console.warn('Failed to play notification sound:', error);
            this.fallbackSound(type, priority);
        }
    }

    playAudioBuffer(audioBuffer, priority) {
        const source = this.audioContext.createBufferSource();
        const gainNode = this.audioContext.createGain();
        
        source.buffer = audioBuffer;
        
        // Adjust volume based on priority
        let volume = this.volume;
        if (priority === 'urgent') {
            volume = Math.min(1.0, this.volume * 1.5);
        } else if (priority === 'low') {
            volume = this.volume * 0.7;
        }
        
        gainNode.gain.setValueAtTime(volume, this.audioContext.currentTime);
        
        source.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        source.start(0);
    }

    fallbackToHTMLAudio() {
        console.log('Using HTML Audio fallback for notifications');
        this.useHTMLAudio = true;
    }

    fallbackSound(type, priority) {
        if (this.useHTMLAudio) {
            const audio = new Audio(this.sounds[type] || this.sounds.default);
            audio.volume = this.volume;
            audio.play().catch(error => {
                console.warn('Failed to play fallback sound:', error);
            });
        } else {
            // System beep as last resort
            this.systemBeep(priority);
        }
    }

    systemBeep(priority = 'normal') {
        // Create a simple beep using Web Audio API
        if (!this.audioContext) return;

        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        // Different frequencies for different priorities
        const frequencies = {
            'urgent': 800,
            'high': 600,
            'normal': 400,
            'low': 300
        };
        
        oscillator.frequency.setValueAtTime(
            frequencies[priority] || frequencies.normal, 
            this.audioContext.currentTime
        );
        
        oscillator.type = 'sine';
        gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
        gainNode.gain.linearRampToValueAtTime(this.volume * 0.3, this.audioContext.currentTime + 0.01);
        gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.3);
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + 0.3);
    }

    // Public API methods
    enable() {
        this.enabled = true;
        this.savePreferences();
    }

    disable() {
        this.enabled = false;
        this.savePreferences();
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        this.savePreferences();
    }

    isEnabled() {
        return this.enabled;
    }

    getVolume() {
        return this.volume;
    }

    // Test sound functionality
    testSound(type = 'default') {
        this.playSound(type, 'normal');
    }
}

// Initialize global notification sound manager
window.notificationSounds = new NotificationSoundManager();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationSoundManager;
}
