<?php

namespace App\Services;

use App\Models\OwnerNotification;
use App\Models\Business;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class NotificationTemplateService
{
    /**
     * Get notification templates for different types.
     */
    public function getTemplates($businessId = null, $type = null): array
    {
        $cacheKey = "notification_templates_{$businessId}_{$type}";
        
        return Cache::remember($cacheKey, 3600, function () use ($businessId, $type) {
            $templates = $this->getDefaultTemplates();
            
            // Filter by type if specified
            if ($type) {
                return $templates[$type] ?? [];
            }
            
            // Add business-specific customizations if needed
            if ($businessId) {
                $templates = $this->applyBusinessCustomizations($templates, $businessId);
            }
            
            return $templates;
        });
    }

    /**
     * Get default notification templates.
     */
    private function getDefaultTemplates(): array
    {
        return [
            'booking' => [
                'new_booking' => [
                    'title' => 'New Booking Received',
                    'message' => 'You have received a new booking for {service_name} on {booking_date} at {booking_time}.',
                    'variables' => ['service_name', 'booking_date', 'booking_time', 'customer_name', 'customer_phone', 'booking_value'],
                    'priority' => 'high',
                    'icon' => 'fas fa-calendar-check',
                    'color' => 'success'
                ],
                'booking_confirmed' => [
                    'title' => 'Booking Confirmed',
                    'message' => 'Booking for {customer_name} has been confirmed for {service_name} on {booking_date}.',
                    'variables' => ['customer_name', 'service_name', 'booking_date', 'booking_time'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-check-circle',
                    'color' => 'success'
                ],
                'booking_reminder' => [
                    'title' => 'Upcoming Booking Reminder',
                    'message' => 'Reminder: {customer_name} has an appointment for {service_name} in {time_until}.',
                    'variables' => ['customer_name', 'service_name', 'time_until', 'booking_date', 'booking_time'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-clock',
                    'color' => 'info'
                ]
            ],
            'cancellation' => [
                'booking_cancelled' => [
                    'title' => 'Booking Cancelled',
                    'message' => '{customer_name} has cancelled their booking for {service_name} on {booking_date}.',
                    'variables' => ['customer_name', 'service_name', 'booking_date', 'cancellation_reason'],
                    'priority' => 'high',
                    'icon' => 'fas fa-calendar-times',
                    'color' => 'warning'
                ],
                'no_show' => [
                    'title' => 'Customer No-Show',
                    'message' => '{customer_name} did not show up for their {service_name} appointment.',
                    'variables' => ['customer_name', 'service_name', 'booking_date', 'booking_time'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-user-times',
                    'color' => 'danger'
                ]
            ],
            'payment' => [
                'payment_received' => [
                    'title' => 'Payment Received',
                    'message' => 'Payment of {amount} received from {customer_name} for {service_name}.',
                    'variables' => ['amount', 'customer_name', 'service_name', 'payment_method', 'transaction_id'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-credit-card',
                    'color' => 'success'
                ],
                'payment_failed' => [
                    'title' => 'Payment Failed',
                    'message' => 'Payment of {amount} failed for {customer_name}. Reason: {failure_reason}',
                    'variables' => ['amount', 'customer_name', 'failure_reason', 'booking_id'],
                    'priority' => 'high',
                    'icon' => 'fas fa-exclamation-triangle',
                    'color' => 'danger'
                ],
                'refund_processed' => [
                    'title' => 'Refund Processed',
                    'message' => 'Refund of {amount} has been processed for {customer_name}.',
                    'variables' => ['amount', 'customer_name', 'refund_reason', 'transaction_id'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-undo',
                    'color' => 'info'
                ]
            ],
            'review' => [
                'new_review' => [
                    'title' => 'New Customer Review',
                    'message' => '{customer_name} left a {rating}-star review for {service_name}.',
                    'variables' => ['customer_name', 'rating', 'service_name', 'review_text'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-star',
                    'color' => 'warning'
                ],
                'review_response_needed' => [
                    'title' => 'Review Response Needed',
                    'message' => 'A {rating}-star review from {customer_name} needs your response.',
                    'variables' => ['customer_name', 'rating', 'review_text', 'days_since_review'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-reply',
                    'color' => 'info'
                ]
            ],
            'customer_message' => [
                'new_message' => [
                    'title' => 'New Customer Message',
                    'message' => '{customer_name} sent you a message: "{message_preview}"',
                    'variables' => ['customer_name', 'message_preview', 'message_subject', 'customer_email'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-comment',
                    'color' => 'primary'
                ],
                'urgent_message' => [
                    'title' => 'Urgent Customer Message',
                    'message' => 'URGENT: {customer_name} needs immediate assistance.',
                    'variables' => ['customer_name', 'message_preview', 'customer_phone'],
                    'priority' => 'urgent',
                    'icon' => 'fas fa-exclamation-circle',
                    'color' => 'danger'
                ]
            ],
            'waiting_list' => [
                'new_entry' => [
                    'title' => 'New Waiting List Entry',
                    'message' => '{customer_name} joined the waiting list for {service_name} on {preferred_date}.',
                    'variables' => ['customer_name', 'service_name', 'preferred_date', 'customer_phone'],
                    'priority' => 'normal',
                    'icon' => 'fas fa-list',
                    'color' => 'info'
                ],
                'slot_available' => [
                    'title' => 'Slot Available for Waiting List',
                    'message' => 'A slot is now available for {service_name} on {available_date}. Contact waiting list customers.',
                    'variables' => ['service_name', 'available_date', 'available_time', 'waiting_count'],
                    'priority' => 'high',
                    'icon' => 'fas fa-calendar-plus',
                    'color' => 'success'
                ]
            ],
            'system' => [
                'maintenance' => [
                    'title' => 'System Maintenance',
                    'message' => 'System maintenance scheduled for {maintenance_date}. Duration: {duration}',
                    'variables' => ['maintenance_date', 'duration', 'affected_services'],
                    'priority' => 'high',
                    'icon' => 'fas fa-tools',
                    'color' => 'warning'
                ],
                'feature_update' => [
                    'title' => 'New Feature Available',
                    'message' => 'New feature "{feature_name}" is now available in your dashboard.',
                    'variables' => ['feature_name', 'feature_description', 'learn_more_url'],
                    'priority' => 'low',
                    'icon' => 'fas fa-star',
                    'color' => 'info'
                ],
                'security_alert' => [
                    'title' => 'Security Alert',
                    'message' => 'Security alert: {alert_message}. Please review your account.',
                    'variables' => ['alert_message', 'alert_type', 'action_required'],
                    'priority' => 'urgent',
                    'icon' => 'fas fa-shield-alt',
                    'color' => 'danger'
                ]
            ],
            'marketing' => [
                'promotion_opportunity' => [
                    'title' => 'Promotion Opportunity',
                    'message' => 'Consider running a promotion for {service_name}. {opportunity_reason}',
                    'variables' => ['service_name', 'opportunity_reason', 'suggested_discount'],
                    'priority' => 'low',
                    'icon' => 'fas fa-bullhorn',
                    'color' => 'purple'
                ],
                'seasonal_reminder' => [
                    'title' => 'Seasonal Marketing Reminder',
                    'message' => '{season} is approaching. Time to update your {marketing_type} campaigns.',
                    'variables' => ['season', 'marketing_type', 'suggested_actions'],
                    'priority' => 'low',
                    'icon' => 'fas fa-calendar-alt',
                    'color' => 'info'
                ]
            ],
            'alert' => [
                'resource_unavailable' => [
                    'title' => 'Resource Unavailable',
                    'message' => '{resource_name} is currently unavailable. {affected_bookings} bookings may be affected.',
                    'variables' => ['resource_name', 'affected_bookings', 'estimated_resolution'],
                    'priority' => 'urgent',
                    'icon' => 'fas fa-exclamation-triangle',
                    'color' => 'danger'
                ],
                'capacity_warning' => [
                    'title' => 'Capacity Warning',
                    'message' => 'You are approaching capacity for {date}. Consider adjusting availability.',
                    'variables' => ['date', 'current_bookings', 'max_capacity'],
                    'priority' => 'high',
                    'icon' => 'fas fa-chart-line',
                    'color' => 'warning'
                ]
            ]
        ];
    }

    /**
     * Apply business-specific customizations to templates.
     */
    private function applyBusinessCustomizations(array $templates, int $businessId): array
    {
        // This could load business-specific template customizations from database
        // For now, return templates as-is
        return $templates;
    }

    /**
     * Create notification from template.
     */
    public function createFromTemplate(
        string $type,
        string $templateKey,
        array $variables,
        int $businessId,
        int $ownerId,
        array $options = []
    ): ?OwnerNotification {
        try {
            $templates = $this->getTemplates($businessId, $type);
            
            if (!isset($templates[$templateKey])) {
                Log::warning("Notification template not found: {$type}.{$templateKey}");
                return null;
            }
            
            $template = $templates[$templateKey];
            
            // Replace variables in title and message
            $title = $this->replaceVariables($template['title'], $variables);
            $message = $this->replaceVariables($template['message'], $variables);
            
            // Create notification
            return OwnerNotification::create([
                'business_id' => $businessId,
                'owner_id' => $ownerId,
                'notification_type' => $type,
                'title' => $title,
                'message' => $message,
                'data' => array_merge($variables, [
                    'template_used' => "{$type}.{$templateKey}",
                    'template_variables' => array_keys($variables)
                ]),
                'priority' => $options['priority'] ?? $template['priority'],
                'source_type' => $options['source_type'] ?? null,
                'source_id' => $options['source_id'] ?? null,
                'expires_at' => $options['expires_at'] ?? null,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to create notification from template', [
                'type' => $type,
                'template' => $templateKey,
                'business_id' => $businessId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Replace variables in template text.
     */
    private function replaceVariables(string $text, array $variables): string
    {
        foreach ($variables as $key => $value) {
            $text = str_replace("{{$key}}", $value, $text);
        }
        
        // Remove any unreplaced variables
        $text = preg_replace('/\{[^}]+\}/', '[missing]', $text);
        
        return $text;
    }

    /**
     * Get available variables for a template type.
     */
    public function getTemplateVariables(string $type, string $templateKey = null): array
    {
        $templates = $this->getTemplates(null, $type);
        
        if ($templateKey && isset($templates[$templateKey])) {
            return $templates[$templateKey]['variables'] ?? [];
        }
        
        // Return all variables for the type
        $allVariables = [];
        foreach ($templates as $template) {
            $allVariables = array_merge($allVariables, $template['variables'] ?? []);
        }
        
        return array_unique($allVariables);
    }

    /**
     * Validate template variables.
     */
    public function validateTemplateVariables(string $type, string $templateKey, array $variables): array
    {
        $requiredVariables = $this->getTemplateVariables($type, $templateKey);
        $missing = [];
        
        foreach ($requiredVariables as $variable) {
            if (!isset($variables[$variable]) || $variables[$variable] === null || $variables[$variable] === '') {
                $missing[] = $variable;
            }
        }
        
        return $missing;
    }
}
