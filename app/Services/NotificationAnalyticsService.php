<?php

namespace App\Services;

use App\Models\OwnerNotification;
use App\Models\Business;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class NotificationAnalyticsService
{
    /**
     * Get comprehensive notification analytics for a business.
     */
    public function getAnalytics($businessId, $ownerId, $period = '30_days'): array
    {
        $cacheKey = "notification_analytics_{$businessId}_{$ownerId}_{$period}";
        
        return Cache::remember($cacheKey, 300, function () use ($businessId, $ownerId, $period) {
            $dateRange = $this->getDateRange($period);
            
            return [
                'overview' => $this->getOverviewStats($businessId, $ownerId, $dateRange),
                'trends' => $this->getTrendAnalysis($businessId, $ownerId, $dateRange),
                'distribution' => $this->getDistributionAnalysis($businessId, $ownerId, $dateRange),
                'performance' => $this->getPerformanceMetrics($businessId, $ownerId, $dateRange),
                'insights' => $this->getInsights($businessId, $ownerId, $dateRange),
                'recommendations' => $this->getRecommendations($businessId, $ownerId, $dateRange)
            ];
        });
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats($businessId, $ownerId, $dateRange): array
    {
        $query = OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId)
            ->whereBetween('created_at', $dateRange);

        $total = $query->count();
        $unread = $query->where('is_read', false)->count();
        $urgent = $query->where('priority', 'urgent')->count();
        $deleted = $query->where('is_deleted', true)->count();

        // Calculate read rate
        $readRate = $total > 0 ? (($total - $unread) / $total) * 100 : 0;

        // Calculate average response time (time to mark as read)
        $avgResponseTime = $this->calculateAverageResponseTime($businessId, $ownerId, $dateRange);

        return [
            'total_notifications' => $total,
            'unread_notifications' => $unread,
            'urgent_notifications' => $urgent,
            'deleted_notifications' => $deleted,
            'read_rate' => round($readRate, 2),
            'average_response_time_hours' => $avgResponseTime
        ];
    }

    /**
     * Get trend analysis.
     */
    private function getTrendAnalysis($businessId, $ownerId, $dateRange): array
    {
        $notifications = OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId)
            ->whereBetween('created_at', $dateRange)
            ->select('created_at', 'notification_type', 'priority', 'is_read')
            ->get();

        // Group by day
        $dailyStats = $notifications->groupBy(function ($notification) {
            return $notification->created_at->format('Y-m-d');
        })->map(function ($dayNotifications) {
            return [
                'total' => $dayNotifications->count(),
                'unread' => $dayNotifications->where('is_read', false)->count(),
                'urgent' => $dayNotifications->where('priority', 'urgent')->count(),
                'types' => $dayNotifications->groupBy('notification_type')->map->count()
            ];
        });

        // Calculate growth rate
        $currentPeriod = $notifications->count();
        $previousPeriod = $this->getPreviousPeriodCount($businessId, $ownerId, $dateRange);
        $growthRate = $previousPeriod > 0 ? (($currentPeriod - $previousPeriod) / $previousPeriod) * 100 : 0;

        return [
            'daily_stats' => $dailyStats,
            'growth_rate' => round($growthRate, 2),
            'peak_day' => $dailyStats->sortByDesc('total')->keys()->first(),
            'busiest_hour' => $this->getBusiestHour($notifications)
        ];
    }

    /**
     * Get distribution analysis.
     */
    private function getDistributionAnalysis($businessId, $ownerId, $dateRange): array
    {
        $notifications = OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId)
            ->whereBetween('created_at', $dateRange)
            ->get();

        return [
            'by_type' => $notifications->groupBy('notification_type')->map->count(),
            'by_priority' => $notifications->groupBy('priority')->map->count(),
            'by_source' => $notifications->groupBy('source_type')->map->count(),
            'by_hour' => $notifications->groupBy(function ($notification) {
                return $notification->created_at->format('H');
            })->map->count(),
            'by_day_of_week' => $notifications->groupBy(function ($notification) {
                return $notification->created_at->format('l');
            })->map->count()
        ];
    }

    /**
     * Get performance metrics.
     */
    private function getPerformanceMetrics($businessId, $ownerId, $dateRange): array
    {
        $notifications = OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId)
            ->whereBetween('created_at', $dateRange)
            ->get();

        $readNotifications = $notifications->where('is_read', true);
        
        // Calculate response times by priority
        $responseTimesByPriority = $readNotifications->groupBy('priority')->map(function ($priorityNotifications) {
            return $priorityNotifications->map(function ($notification) {
                if ($notification->read_at) {
                    return $notification->created_at->diffInMinutes($notification->read_at);
                }
                return null;
            })->filter()->avg();
        });

        // Calculate engagement metrics
        $engagementRate = $notifications->count() > 0 ? 
            ($readNotifications->count() / $notifications->count()) * 100 : 0;

        return [
            'engagement_rate' => round($engagementRate, 2),
            'response_times_by_priority' => $responseTimesByPriority->map(function ($time) {
                return $time ? round($time, 2) : 0;
            }),
            'fastest_response_time' => $this->getFastestResponseTime($readNotifications),
            'slowest_response_time' => $this->getSlowestResponseTime($readNotifications),
            'notification_frequency' => $this->getNotificationFrequency($notifications, $dateRange)
        ];
    }

    /**
     * Get actionable insights.
     */
    private function getInsights($businessId, $ownerId, $dateRange): array
    {
        $notifications = OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId)
            ->whereBetween('created_at', $dateRange)
            ->get();

        $insights = [];

        // High unread rate insight
        $unreadRate = $notifications->count() > 0 ? 
            ($notifications->where('is_read', false)->count() / $notifications->count()) * 100 : 0;
        
        if ($unreadRate > 30) {
            $insights[] = [
                'type' => 'warning',
                'title' => 'High Unread Rate',
                'message' => "You have a {$unreadRate}% unread rate. Consider reviewing your notification preferences.",
                'action' => 'Review notification settings'
            ];
        }

        // Urgent notifications insight
        $urgentCount = $notifications->where('priority', 'urgent')->count();
        if ($urgentCount > 5) {
            $insights[] = [
                'type' => 'alert',
                'title' => 'Multiple Urgent Notifications',
                'message' => "You have {$urgentCount} urgent notifications that need immediate attention.",
                'action' => 'Review urgent notifications'
            ];
        }

        // Peak time insight
        $busiestHour = $this->getBusiestHour($notifications);
        if ($busiestHour) {
            $insights[] = [
                'type' => 'info',
                'title' => 'Peak Notification Time',
                'message' => "Most notifications arrive at {$busiestHour}:00. Consider adjusting quiet hours if needed.",
                'action' => 'Adjust notification schedule'
            ];
        }

        // Type distribution insight
        $typeDistribution = $notifications->groupBy('notification_type')->map->count();
        $dominantType = $typeDistribution->sortDesc()->keys()->first();
        $dominantTypeCount = $typeDistribution->max();
        $dominantTypePercentage = $notifications->count() > 0 ? 
            ($dominantTypeCount / $notifications->count()) * 100 : 0;

        if ($dominantTypePercentage > 50) {
            $insights[] = [
                'type' => 'info',
                'title' => 'Notification Type Pattern',
                'message' => "{$dominantTypePercentage}% of your notifications are {$dominantType} related.",
                'action' => 'Consider customizing ' . $dominantType . ' notification settings'
            ];
        }

        return $insights;
    }

    /**
     * Get recommendations.
     */
    private function getRecommendations($businessId, $ownerId, $dateRange): array
    {
        $notifications = OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId)
            ->whereBetween('created_at', $dateRange)
            ->get();

        $recommendations = [];

        // Response time recommendation
        $avgResponseTime = $this->calculateAverageResponseTime($businessId, $ownerId, $dateRange);
        if ($avgResponseTime > 24) {
            $recommendations[] = [
                'priority' => 'high',
                'title' => 'Improve Response Time',
                'description' => 'Your average response time is over 24 hours. Consider enabling push notifications for urgent alerts.',
                'action' => 'Enable push notifications',
                'impact' => 'Better customer service and faster issue resolution'
            ];
        }

        // Notification volume recommendation
        $dailyAverage = $notifications->count() / max(1, $this->getDaysBetween($dateRange));
        if ($dailyAverage > 20) {
            $recommendations[] = [
                'priority' => 'medium',
                'title' => 'High Notification Volume',
                'description' => 'You receive an average of ' . round($dailyAverage) . ' notifications per day. Consider using digest mode.',
                'action' => 'Enable daily digest',
                'impact' => 'Reduced notification fatigue and better focus'
            ];
        }

        // Quiet hours recommendation
        $nightNotifications = $notifications->filter(function ($notification) {
            $hour = $notification->created_at->format('H');
            return $hour >= 22 || $hour <= 6;
        })->count();

        if ($nightNotifications > 5) {
            $recommendations[] = [
                'priority' => 'medium',
                'title' => 'Set Quiet Hours',
                'description' => "You received {$nightNotifications} notifications during night hours. Consider setting quiet hours.",
                'action' => 'Configure quiet hours',
                'impact' => 'Better work-life balance and uninterrupted rest'
            ];
        }

        // Priority filter recommendation
        $lowPriorityCount = $notifications->where('priority', 'low')->count();
        $lowPriorityPercentage = $notifications->count() > 0 ? 
            ($lowPriorityCount / $notifications->count()) * 100 : 0;

        if ($lowPriorityPercentage > 40) {
            $recommendations[] = [
                'priority' => 'low',
                'title' => 'Filter Low Priority Notifications',
                'description' => "{$lowPriorityPercentage}% of your notifications are low priority. Consider filtering them.",
                'action' => 'Set minimum priority filter',
                'impact' => 'Focus on important notifications only'
            ];
        }

        return $recommendations;
    }

    /**
     * Helper methods
     */
    private function getDateRange($period): array
    {
        $end = Carbon::now();
        
        switch ($period) {
            case '7_days':
                $start = $end->copy()->subDays(7);
                break;
            case '30_days':
                $start = $end->copy()->subDays(30);
                break;
            case '90_days':
                $start = $end->copy()->subDays(90);
                break;
            case '1_year':
                $start = $end->copy()->subYear();
                break;
            default:
                $start = $end->copy()->subDays(30);
        }

        return [$start, $end];
    }

    private function calculateAverageResponseTime($businessId, $ownerId, $dateRange): float
    {
        $readNotifications = OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId)
            ->whereBetween('created_at', $dateRange)
            ->where('is_read', true)
            ->whereNotNull('read_at')
            ->get();

        if ($readNotifications->isEmpty()) {
            return 0;
        }

        $totalMinutes = $readNotifications->sum(function ($notification) {
            return $notification->created_at->diffInMinutes($notification->read_at);
        });

        return round($totalMinutes / $readNotifications->count() / 60, 2); // Convert to hours
    }

    private function getPreviousPeriodCount($businessId, $ownerId, $dateRange): int
    {
        $periodLength = $dateRange[0]->diffInDays($dateRange[1]);
        $previousStart = $dateRange[0]->copy()->subDays($periodLength);
        $previousEnd = $dateRange[0];

        return OwnerNotification::where('business_id', $businessId)
            ->where('owner_id', $ownerId)
            ->whereBetween('created_at', [$previousStart, $previousEnd])
            ->count();
    }

    private function getBusiestHour($notifications): ?string
    {
        $hourCounts = $notifications->groupBy(function ($notification) {
            return $notification->created_at->format('H');
        })->map->count();

        return $hourCounts->sortDesc()->keys()->first();
    }

    private function getFastestResponseTime($readNotifications): ?float
    {
        if ($readNotifications->isEmpty()) {
            return null;
        }

        $fastestMinutes = $readNotifications->min(function ($notification) {
            return $notification->read_at ? 
                $notification->created_at->diffInMinutes($notification->read_at) : null;
        });

        return $fastestMinutes ? round($fastestMinutes / 60, 2) : null;
    }

    private function getSlowestResponseTime($readNotifications): ?float
    {
        if ($readNotifications->isEmpty()) {
            return null;
        }

        $slowestMinutes = $readNotifications->max(function ($notification) {
            return $notification->read_at ? 
                $notification->created_at->diffInMinutes($notification->read_at) : null;
        });

        return $slowestMinutes ? round($slowestMinutes / 60, 2) : null;
    }

    private function getNotificationFrequency($notifications, $dateRange): float
    {
        $days = $this->getDaysBetween($dateRange);
        return $days > 0 ? round($notifications->count() / $days, 2) : 0;
    }

    private function getDaysBetween($dateRange): int
    {
        return max(1, $dateRange[0]->diffInDays($dateRange[1]));
    }
}
