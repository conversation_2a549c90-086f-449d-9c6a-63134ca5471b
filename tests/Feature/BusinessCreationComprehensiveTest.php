<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Business;
use App\Models\BusinessCategory;
use App\Models\BusinessLandingPage;
use App\Models\LandingServiceSettings;
use App\Models\BusinessSeoSettings;
use App\Services\BusinessCreationValidationService;
use App\Services\EnterpriseIsolationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Auth;

class BusinessCreationComprehensiveTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $owner;
    protected $category;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test owner
        $this->owner = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password')
        ]);
        $this->owner->assignRole('Business Owner');

        // Create test category
        $this->category = BusinessCategory::factory()->create([
            'name' => 'Test Category',
            'slug' => 'test-category'
        ]);
    }

    /** @test */
    public function it_can_create_business_with_all_features()
    {
        $this->actingAs($this->owner);

        $businessData = [
            // Step 1: Business Information
            'name' => 'Test Business',
            'category_id' => $this->category->id,
            'description' => 'A comprehensive test business',
            'phone' => '+1234567890',
            'email' => '<EMAIL>',
            'website' => 'https://testbusiness.com',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',

            // Step 2: Landing Page Setup
            'landing_page_slug' => 'test-business-slug',
            'domain_type' => 'subdirectory',
            'page_title' => 'Test Business Landing Page',
            'landing_page_theme' => 'modern',
            'page_description' => 'Welcome to our test business',
            'booking_enabled' => true,

            // Step 3: Service Display Configuration
            'service_display_type' => 'grid',
            'service_count' => 6,
            'grid_columns' => 3,
            'service_card_style' => 'modern',
            'show_service_pricing' => true,
            'show_service_duration' => true,
            'show_service_descriptions' => true,
            'show_service_images' => true,
            'show_service_categories' => true,
            'enable_service_search' => true,
            'enable_service_filtering' => true,
            'quick_booking_enabled' => true,
            'show_availability_status' => true,
            'show_reviews_rating' => true,

            // Advanced Service Features
            'enable_service_seo' => true,
            'generate_service_sitemap' => true,
            'group_by_category' => true,
            'track_service_analytics' => true,
            'mobile_optimized' => true,
            'show_booking_calendar' => true,

            // Booking Configuration
            'booking_button_text' => 'Book Now',
            'booking_button_style' => 'primary',

            // Advanced Service Management
            'enable_service_packages' => true,
            'enable_add_on_services' => true,
            'enable_staff_assignments' => true,
            'enable_seasonal_services' => false,
            'enable_service_prerequisites' => false,
            'enable_equipment_requirements' => false,
            'enable_service_faqs' => true,
            'enable_before_after_gallery' => true,

            // Visual Page Builder
            'enable_visual_editor' => true,
            'enable_live_preview' => true,
            'enable_modular_sections' => true,
            'enable_wysiwyg_editor' => true,
            'enable_multimedia_management' => true,
            'enable_pre_designed_blocks' => true,

            // Default Sections
            'include_hero_section' => true,
            'include_about_section' => true,
            'include_services_section' => true,
            'include_team_section' => false,
            'include_testimonials_section' => true,
            'include_gallery_section' => true,
            'include_contact_section' => true,
            'include_booking_section' => true,

            // Step 4: SEO Settings
            'meta_title' => 'Test Business - Professional Services',
            'meta_description' => 'Professional services from Test Business. Book your appointment today.',
            'meta_keywords' => 'test, business, services, professional',
            'enable_open_graph' => true,
            'enable_twitter_cards' => true,
            'enable_schema_markup' => true,
            'enable_local_seo' => true,
            'enable_amp' => false,
            'enable_lazy_loading' => true,
            'google_analytics_id' => 'GA-123456789',
            'google_tag_manager_id' => 'GTM-123456',
            'facebook_pixel_id' => 'FB-123456789',

            // Step 5: Terms acceptance
            'terms_accepted' => true
        ];

        $response = $this->post(route('owner.business.store'), $businessData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        // Verify business was created
        $business = Business::where('name', 'Test Business')->first();
        $this->assertNotNull($business);
        $this->assertEquals($this->owner->id, $business->owner_id);

        // Verify landing page was created
        $landingPage = $business->landingPage;
        $this->assertNotNull($landingPage);
        $this->assertEquals('test-business-slug', $landingPage->custom_slug);
        $this->assertEquals('subdirectory', $landingPage->domain_type);
        $this->assertEquals('modern', $landingPage->theme);

        // Verify service settings were created
        $serviceSettings = $business->landingServiceSettings;
        $this->assertNotNull($serviceSettings);
        $this->assertEquals('grid', $serviceSettings->layout_type);
        $this->assertEquals(6, $serviceSettings->homepage_display_count);
        $this->assertTrue($serviceSettings->show_pricing);
        $this->assertTrue($serviceSettings->enable_quick_booking);

        // Verify SEO settings were created
        $seoSettings = $business->seoSettings;
        $this->assertNotNull($seoSettings);
        $this->assertEquals('Test Business - Professional Services', $seoSettings->meta_title);
        $this->assertEquals('GA-123456789', $seoSettings->google_analytics_id);

        return $business;
    }

    /** @test */
    public function it_validates_business_creation_completeness()
    {
        $business = $this->it_can_create_business_with_all_features();

        $validationService = new BusinessCreationValidationService();
        $results = $validationService->validateBusinessCreation($business->id);

        $this->assertEquals('PASSED', $results['status']);
        $this->assertArrayHasKey('validations', $results);
        
        // Check that all validation categories passed
        $validationCategories = [
            'business_basic_info',
            'landing_page_setup',
            'service_display_config',
            'seo_optimization',
            'enterprise_isolation',
            'advanced_features',
            'database_integrity',
            'security_compliance'
        ];

        foreach ($validationCategories as $category) {
            $this->assertArrayHasKey($category, $results['validations']);
            $this->assertContains($results['validations'][$category]['status'], ['PASS', 'WARNING']);
        }
    }

    /** @test */
    public function it_enforces_enterprise_isolation()
    {
        // Create first business
        $business1 = $this->it_can_create_business_with_all_features();

        // Create second owner and business
        $owner2 = User::factory()->create();
        $owner2->assignRole('Business Owner');
        
        $this->actingAs($owner2);
        
        $business2Data = [
            'name' => 'Second Business',
            'category_id' => $this->category->id,
            'landing_page_slug' => 'second-business-slug',
            'domain_type' => 'subdirectory',
            'page_title' => 'Second Business',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'terms_accepted' => true
        ];

        $response = $this->post(route('owner.business.store'), $business2Data);
        $business2 = Business::where('name', 'Second Business')->first();

        // Test enterprise isolation
        $isolationService = new EnterpriseIsolationService();

        // Owner 1 should only access business 1
        $this->assertTrue($isolationService->validateBusinessAccess($business1->id, $this->owner->id));
        $this->assertFalse($isolationService->validateBusinessAccess($business2->id, $this->owner->id));

        // Owner 2 should only access business 2
        $this->assertFalse($isolationService->validateBusinessAccess($business1->id, $owner2->id));
        $this->assertTrue($isolationService->validateBusinessAccess($business2->id, $owner2->id));

        // Test allowed business IDs
        $allowedIds1 = $isolationService->getAllowedBusinessIds($this->owner->id);
        $allowedIds2 = $isolationService->getAllowedBusinessIds($owner2->id);

        $this->assertContains($business1->id, $allowedIds1);
        $this->assertNotContains($business2->id, $allowedIds1);

        $this->assertContains($business2->id, $allowedIds2);
        $this->assertNotContains($business1->id, $allowedIds2);
    }

    /** @test */
    public function it_validates_unique_slug_constraint()
    {
        $this->actingAs($this->owner);

        // Create first business
        $businessData1 = [
            'name' => 'First Business',
            'category_id' => $this->category->id,
            'landing_page_slug' => 'unique-slug',
            'domain_type' => 'subdirectory',
            'page_title' => 'First Business',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'terms_accepted' => true
        ];

        $response1 = $this->post(route('owner.business.store'), $businessData1);
        $response1->assertRedirect();

        // Try to create second business with same slug
        $owner2 = User::factory()->create();
        $owner2->assignRole('Business Owner');
        $this->actingAs($owner2);

        $businessData2 = [
            'name' => 'Second Business',
            'category_id' => $this->category->id,
            'landing_page_slug' => 'unique-slug', // Same slug
            'domain_type' => 'subdirectory',
            'page_title' => 'Second Business',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'terms_accepted' => true
        ];

        $response2 = $this->post(route('owner.business.store'), $businessData2);
        $response2->assertSessionHasErrors('landing_page_slug');
    }

    /** @test */
    public function it_prevents_multiple_businesses_per_owner()
    {
        $this->actingAs($this->owner);

        // Create first business
        $this->it_can_create_business_with_all_features();

        // Try to create second business with same owner
        $businessData2 = [
            'name' => 'Second Business',
            'category_id' => $this->category->id,
            'landing_page_slug' => 'second-business',
            'domain_type' => 'subdirectory',
            'page_title' => 'Second Business',
            'timezone' => 'America/New_York',
            'currency' => 'USD',
            'language' => 'en',
            'terms_accepted' => true
        ];

        $response = $this->post(route('owner.business.store'), $businessData2);
        $response->assertRedirect(route('owner.dashboard'));
        $response->assertSessionHas('info');
    }

    /** @test */
    public function it_runs_comprehensive_isolation_audit()
    {
        // Create test businesses
        $business1 = $this->it_can_create_business_with_all_features();
        
        $isolationService = new EnterpriseIsolationService();
        $auditResults = $isolationService->runIsolationAudit();

        $this->assertArrayHasKey('checks', $auditResults);
        $this->assertArrayHasKey('summary', $auditResults);
        
        // All checks should pass for a properly created business
        $failedChecks = collect($auditResults['checks'])->where('status', 'FAIL');
        $this->assertEmpty($failedChecks, 'Enterprise isolation audit should pass all checks');
    }
}
