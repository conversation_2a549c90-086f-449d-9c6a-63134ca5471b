<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('owner_notification_preferences', function (Blueprint $table) {
            // Add the unique constraint with a shorter name
            try {
                $table->unique(['owner_id', 'business_id', 'notification_type'], 'owner_notif_prefs_unique');
            } catch (\Exception $e) {
                // Constraint might already exist, ignore the error
            }
            
            // Add missing indexes if they don't exist
            try {
                $table->index(['owner_id', 'business_id'], 'owner_notif_prefs_owner_business_idx');
            } catch (\Exception $e) {
                // Index might already exist, ignore the error
            }
            
            try {
                $table->index(['notification_type'], 'owner_notif_prefs_type_idx');
            } catch (\Exception $e) {
                // Index might already exist, ignore the error
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('owner_notification_preferences', function (Blueprint $table) {
            $table->dropUnique('owner_notif_prefs_unique');
            $table->dropIndex('owner_notif_prefs_owner_business_idx');
            $table->dropIndex('owner_notif_prefs_type_idx');
        });
    }
};
