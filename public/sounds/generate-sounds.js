/**
 * Sound Generator for Notification System
 * Generates notification sounds programmatically using Web Audio API
 */

class NotificationSoundGenerator {
    constructor() {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        this.sampleRate = this.audioContext.sampleRate;
    }

    // Generate a pleasant notification sound
    generateNotificationTone(frequency = 440, duration = 0.3, type = 'sine') {
        const length = this.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, length, this.sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < length; i++) {
            const t = i / this.sampleRate;
            let sample = 0;

            switch (type) {
                case 'sine':
                    sample = Math.sin(2 * Math.PI * frequency * t);
                    break;
                case 'triangle':
                    sample = (2 / Math.PI) * Math.asin(Math.sin(2 * Math.PI * frequency * t));
                    break;
                case 'square':
                    sample = Math.sign(Math.sin(2 * Math.PI * frequency * t));
                    break;
                case 'sawtooth':
                    sample = 2 * (t * frequency - Math.floor(0.5 + t * frequency));
                    break;
            }

            // Apply envelope (fade in/out)
            const envelope = this.getEnvelope(t, duration);
            data[i] = sample * envelope * 0.3; // Reduce volume
        }

        return buffer;
    }

    // Generate envelope for smooth sound
    getEnvelope(time, duration) {
        const attack = 0.01;
        const decay = 0.1;
        const sustain = 0.7;
        const release = duration - decay - attack;

        if (time < attack) {
            return time / attack;
        } else if (time < attack + decay) {
            return 1 - (1 - sustain) * (time - attack) / decay;
        } else if (time < duration - release) {
            return sustain;
        } else {
            return sustain * (duration - time) / release;
        }
    }

    // Generate chord progression
    generateChord(frequencies, duration = 0.5) {
        const length = this.sampleRate * duration;
        const buffer = this.audioContext.createBuffer(1, length, this.sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < length; i++) {
            const t = i / this.sampleRate;
            let sample = 0;

            frequencies.forEach(freq => {
                sample += Math.sin(2 * Math.PI * freq * t) / frequencies.length;
            });

            const envelope = this.getEnvelope(t, duration);
            data[i] = sample * envelope * 0.2;
        }

        return buffer;
    }

    // Play generated sound
    playBuffer(buffer) {
        const source = this.audioContext.createBufferSource();
        source.buffer = buffer;
        source.connect(this.audioContext.destination);
        source.start();
    }

    // Generate specific notification sounds
    generateBookingSound() {
        // Pleasant ascending chord
        return this.generateChord([261.63, 329.63, 392.00], 0.4); // C-E-G major chord
    }

    generateCancellationSound() {
        // Descending tone
        const length = this.sampleRate * 0.5;
        const buffer = this.audioContext.createBuffer(1, length, this.sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < length; i++) {
            const t = i / this.sampleRate;
            const frequency = 440 - (t * 200); // Descending from 440Hz to 240Hz
            const sample = Math.sin(2 * Math.PI * frequency * t);
            const envelope = this.getEnvelope(t, 0.5);
            data[i] = sample * envelope * 0.3;
        }

        return buffer;
    }

    generatePaymentSound() {
        // Cash register-like sound
        return this.generateChord([523.25, 659.25, 783.99], 0.3); // C5-E5-G5
    }

    generateAlertSound() {
        // Urgent beeping
        const length = this.sampleRate * 0.6;
        const buffer = this.audioContext.createBuffer(1, length, this.sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < length; i++) {
            const t = i / this.sampleRate;
            const beepFreq = 800;
            const beepDuration = 0.1;
            const beepInterval = 0.15;
            
            const beepPhase = t % beepInterval;
            let sample = 0;
            
            if (beepPhase < beepDuration) {
                sample = Math.sin(2 * Math.PI * beepFreq * t);
                const beepEnv = Math.min(1, beepPhase / 0.01, (beepDuration - beepPhase) / 0.01);
                sample *= beepEnv;
            }
            
            data[i] = sample * 0.4;
        }

        return buffer;
    }

    generateDefaultSound() {
        // Simple pleasant notification
        return this.generateNotificationTone(523.25, 0.3, 'sine'); // C5
    }

    // Export sound as WAV blob (for saving)
    bufferToWav(buffer) {
        const length = buffer.length;
        const arrayBuffer = new ArrayBuffer(44 + length * 2);
        const view = new DataView(arrayBuffer);
        const data = buffer.getChannelData(0);

        // WAV header
        const writeString = (offset, string) => {
            for (let i = 0; i < string.length; i++) {
                view.setUint8(offset + i, string.charCodeAt(i));
            }
        };

        writeString(0, 'RIFF');
        view.setUint32(4, 36 + length * 2, true);
        writeString(8, 'WAVE');
        writeString(12, 'fmt ');
        view.setUint32(16, 16, true);
        view.setUint16(20, 1, true);
        view.setUint16(22, 1, true);
        view.setUint32(24, this.sampleRate, true);
        view.setUint32(28, this.sampleRate * 2, true);
        view.setUint16(32, 2, true);
        view.setUint16(34, 16, true);
        writeString(36, 'data');
        view.setUint32(40, length * 2, true);

        // Convert float samples to 16-bit PCM
        let offset = 44;
        for (let i = 0; i < length; i++) {
            const sample = Math.max(-1, Math.min(1, data[i]));
            view.setInt16(offset, sample * 0x7FFF, true);
            offset += 2;
        }

        return new Blob([arrayBuffer], { type: 'audio/wav' });
    }
}

// Usage example and sound generation
if (typeof window !== 'undefined') {
    window.generateNotificationSounds = function() {
        const generator = new NotificationSoundGenerator();
        
        // Generate and test sounds
        const sounds = {
            'default': generator.generateDefaultSound(),
            'booking': generator.generateBookingSound(),
            'cancellation': generator.generateCancellationSound(),
            'payment': generator.generatePaymentSound(),
            'alert': generator.generateAlertSound()
        };

        // Test each sound
        Object.keys(sounds).forEach((type, index) => {
            setTimeout(() => {
                console.log(`Playing ${type} sound`);
                generator.playBuffer(sounds[type]);
            }, index * 1000);
        });

        return sounds;
    };
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotificationSoundGenerator;
}
