<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('owner_notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('business_id')->constrained('businesses')->onDelete('cascade');
            $table->enum('notification_type', [
                'booking', 'cancellation', 'payment', 'review', 'system',
                'marketing', 'alert', 'reminder', 'customer_message', 'waiting_list'
            ]);

            // Channel preferences
            $table->boolean('email_enabled')->default(true);
            $table->boolean('sms_enabled')->default(false);
            $table->boolean('push_enabled')->default(true);
            $table->boolean('in_app_enabled')->default(true);
            $table->boolean('sound_enabled')->default(true);

            // Filtering preferences
            $table->enum('priority_filter', ['low', 'normal', 'high', 'urgent'])->nullable();
            $table->time('quiet_hours_start')->nullable();
            $table->time('quiet_hours_end')->nullable();
            $table->boolean('weekend_notifications')->default(true);

            // Advanced preferences
            $table->enum('digest_frequency', ['never', 'daily', 'weekly', 'monthly'])->default('daily');
            $table->boolean('auto_mark_read')->default(false);
            $table->json('settings')->nullable(); // For additional custom settings

            $table->timestamps();

            // Indexes for performance
            $table->index(['owner_id', 'business_id']);
            $table->index(['notification_type']);
            $table->unique(['owner_id', 'business_id', 'notification_type'], 'owner_notif_prefs_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('owner_notification_preferences');
    }
};
