@extends('owner.layouts.app')

@section('title', 'Notification Preferences')

@section('content_header')
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Notification Preferences</h1>
            <p class="text-muted">Customize how and when you receive notifications</p>
        </div>
        <div class="d-flex" style="gap: 10px;">
            <button type="button" class="btn btn-outline-secondary" id="resetPreferencesBtn">
                <i class="fas fa-undo"></i> Reset to Defaults
            </button>
            <button type="button" class="btn btn-outline-info" id="testNotificationBtn">
                <i class="fas fa-bell"></i> Test Notification
            </button>
            <button type="button" class="btn btn-primary" id="savePreferencesBtn">
                <i class="fas fa-save"></i> Save Preferences
            </button>
        </div>
    </div>
@stop

@push('css')
<style>
.preference-card {
    transition: all 0.3s ease;
}
.preference-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.channel-toggle {
    margin-bottom: 10px;
}
.global-settings {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}
.notification-type-card {
    border-left: 4px solid #007bff;
}
.notification-type-card.booking { border-left-color: #28a745; }
.notification-type-card.cancellation { border-left-color: #dc3545; }
.notification-type-card.payment { border-left-color: #17a2b8; }
.notification-type-card.review { border-left-color: #ffc107; }
.notification-type-card.system { border-left-color: #6c757d; }
.notification-type-card.marketing { border-left-color: #007bff; }
.notification-type-card.alert { border-left-color: #dc3545; }
.notification-type-card.reminder { border-left-color: #ffc107; }
.notification-type-card.customer_message { border-left-color: #17a2b8; }
.notification-type-card.waiting_list { border-left-color: #007bff; }
</style>
@endpush

@section('content')
    {{-- Global Settings Card --}}
    <div class="card global-settings mb-4">
        <div class="card-header">
            <h3 class="card-title">
                <i class="fas fa-globe mr-2"></i>
                Global Notification Settings
            </h3>
            <div class="card-tools">
                <button type="button" class="btn btn-tool text-white" data-card-widget="collapse">
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h5>Delivery Channels</h5>
                    <div class="form-group">
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalEmailEnabled">
                            <label class="custom-control-label" for="globalEmailEnabled">
                                <i class="fas fa-envelope mr-2"></i>Email Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalSmsEnabled">
                            <label class="custom-control-label" for="globalSmsEnabled">
                                <i class="fas fa-sms mr-2"></i>SMS Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalPushEnabled">
                            <label class="custom-control-label" for="globalPushEnabled">
                                <i class="fas fa-bell mr-2"></i>Push Notifications
                            </label>
                        </div>
                        <div class="custom-control custom-switch channel-toggle">
                            <input type="checkbox" class="custom-control-input" id="globalSoundEnabled">
                            <label class="custom-control-label" for="globalSoundEnabled">
                                <i class="fas fa-volume-up mr-2"></i>Sound Alerts
                            </label>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>Timing Settings</h5>
                    <div class="form-group">
                        <label>Quiet Hours</label>
                        <div class="row">
                            <div class="col-6">
                                <input type="time" class="form-control" id="globalQuietHoursStart" placeholder="Start">
                            </div>
                            <div class="col-6">
                                <input type="time" class="form-control" id="globalQuietHoursEnd" placeholder="End">
                            </div>
                        </div>
                        <small class="text-muted">No notifications during these hours</small>
                    </div>
                    <div class="form-group">
                        <div class="custom-control custom-switch">
                            <input type="checkbox" class="custom-control-input" id="globalWeekendNotifications">
                            <label class="custom-control-label" for="globalWeekendNotifications">
                                Weekend Notifications
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Digest Frequency</label>
                        <select class="form-control" id="globalDigestFrequency">
                            <option value="never">Never</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="text-center mt-3">
                <button type="button" class="btn btn-light" id="applyGlobalSettingsBtn">
                    <i class="fas fa-magic mr-2"></i>Apply to All Notification Types
                </button>
            </div>
        </div>
    </div>

    {{-- Individual Notification Type Preferences --}}
    <div class="row">
        @foreach($notificationTypes as $type => $label)
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="card preference-card notification-type-card {{ $type }}" data-type="{{ $type }}">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            @php
                                $icons = [
                                    'booking' => 'fas fa-calendar-check',
                                    'cancellation' => 'fas fa-calendar-times',
                                    'payment' => 'fas fa-credit-card',
                                    'review' => 'fas fa-star',
                                    'system' => 'fas fa-cog',
                                    'marketing' => 'fas fa-bullhorn',
                                    'alert' => 'fas fa-exclamation-triangle',
                                    'reminder' => 'fas fa-clock',
                                    'customer_message' => 'fas fa-comment',
                                    'waiting_list' => 'fas fa-list'
                                ];
                            @endphp
                            <i class="{{ $icons[$type] ?? 'fas fa-bell' }} mr-2"></i>
                            {{ $label }}
                        </h5>
                        <div class="card-tools">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input master-toggle"
                                       id="masterToggle{{ ucfirst($type) }}"
                                       data-type="{{ $type }}">
                                <label class="custom-control-label" for="masterToggle{{ ucfirst($type) }}"></label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {{-- Delivery Channels --}}
                        <div class="mb-3">
                            <h6>Delivery Channels</h6>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="email{{ ucfirst($type) }}"
                                       data-type="{{ $type }}"
                                       data-channel="email">
                                <label class="custom-control-label" for="email{{ ucfirst($type) }}">
                                    <i class="fas fa-envelope mr-1"></i>Email
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="sms{{ ucfirst($type) }}"
                                       data-type="{{ $type }}"
                                       data-channel="sms">
                                <label class="custom-control-label" for="sms{{ ucfirst($type) }}">
                                    <i class="fas fa-sms mr-1"></i>SMS
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="push{{ ucfirst($type) }}"
                                       data-type="{{ $type }}"
                                       data-channel="push">
                                <label class="custom-control-label" for="push{{ ucfirst($type) }}">
                                    <i class="fas fa-bell mr-1"></i>Push
                                </label>
                            </div>
                            <div class="custom-control custom-switch channel-toggle">
                                <input type="checkbox" class="custom-control-input channel-toggle-input"
                                       id="inApp{{ ucfirst($type) }}"
                                       data-type="{{ $type }}"
                                       data-channel="in_app">
                                <label class="custom-control-label" for="inApp{{ ucfirst($type) }}">
                                    <i class="fas fa-desktop mr-1"></i>In-App
                                </label>
                            </div>
                        </div>

                        {{-- Priority Filter --}}
                        <div class="mb-3">
                            <label class="form-label">Minimum Priority</label>
                            <select class="form-control form-control-sm priority-filter" data-type="{{ $type }}">
                                <option value="">All Priorities</option>
                                <option value="low">Low and above</option>
                                <option value="normal">Normal and above</option>
                                <option value="high">High and above</option>
                                <option value="urgent">Urgent only</option>
                            </select>
                        </div>

                        {{-- Additional Settings --}}
                        <div class="mb-2">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input auto-mark-read"
                                       id="autoMarkRead{{ ucfirst($type) }}"
                                       data-type="{{ $type }}">
                                <label class="custom-control-label" for="autoMarkRead{{ ucfirst($type) }}">
                                    Auto-mark as read
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    {{-- Test Notification Modal --}}
    <div class="modal fade" id="testNotificationModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">
                        <i class="fas fa-bell mr-2"></i>
                        Test Notification
                    </h4>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label>Notification Type</label>
                        <select class="form-control" id="testNotificationType">
                            @foreach($notificationTypes as $type => $label)
                                <option value="{{ $type }}">{{ $label }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Test Channels</label>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="testEmail" value="email">
                            <label class="custom-control-label" for="testEmail">Email</label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="testSms" value="sms">
                            <label class="custom-control-label" for="testSms">SMS</label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="testPush" value="push">
                            <label class="custom-control-label" for="testPush">Push Notification</label>
                        </div>
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" id="testInApp" value="in_app" checked>
                            <label class="custom-control-label" for="testInApp">In-App Notification</label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="sendTestNotificationBtn">
                        <i class="fas fa-paper-plane mr-2"></i>Send Test
                    </button>
                </div>
            </div>
        </div>
    </div>
@stop

@section('js')
<script>
$(document).ready(function() {
    // CSRF token setup
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Load current preferences
    loadPreferences();

    // Master toggle for each notification type
    $('.master-toggle').change(function() {
        const type = $(this).data('type');
        const enabled = $(this).is(':checked');
        const card = $(`.notification-type-card[data-type="${type}"]`);

        // Toggle all channels for this type
        card.find('.channel-toggle-input').prop('checked', enabled);

        // Enable/disable the card
        card.toggleClass('disabled', !enabled);
        card.find('input, select').not('.master-toggle').prop('disabled', !enabled);
    });

    // Apply global settings to all types
    $('#applyGlobalSettingsBtn').click(function() {
        const globalSettings = {
            email: $('#globalEmailEnabled').is(':checked'),
            sms: $('#globalSmsEnabled').is(':checked'),
            push: $('#globalPushEnabled').is(':checked'),
            sound: $('#globalSoundEnabled').is(':checked'),
            quietStart: $('#globalQuietHoursStart').val(),
            quietEnd: $('#globalQuietHoursEnd').val(),
            weekend: $('#globalWeekendNotifications').is(':checked'),
            digest: $('#globalDigestFrequency').val()
        };

        // Apply to all notification types
        $('.notification-type-card').each(function() {
            const type = $(this).data('type');
            $(this).find(`#email${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.email);
            $(this).find(`#sms${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.sms);
            $(this).find(`#push${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', globalSettings.push);
            $(this).find(`#inApp${type.charAt(0).toUpperCase() + type.slice(1)}`).prop('checked', true); // Always enable in-app
        });

        toastr.success('Global settings applied to all notification types');
    });

    // Save preferences
    $('#savePreferencesBtn').click(function() {
        savePreferences();
    });

    // Reset preferences
    $('#resetPreferencesBtn').click(function() {
        if (confirm('Are you sure you want to reset all preferences to defaults?')) {
            resetPreferences();
        }
    });

    // Test notification
    $('#testNotificationBtn').click(function() {
        $('#testNotificationModal').modal('show');
    });

    // Send test notification
    $('#sendTestNotificationBtn').click(function() {
        sendTestNotification();
    });

    function loadPreferences() {
        // Load preferences from server-side data
        @foreach($preferences as $preference)
            const type{{ ucfirst($preference->notification_type) }} = @json($preference);
            loadPreferenceData('{{ $preference->notification_type }}', type{{ ucfirst($preference->notification_type) }});
        @endforeach
    }

    function loadPreferenceData(type, data) {
        const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);

        // Set channel toggles
        $(`#email${typeCapitalized}`).prop('checked', data.email_enabled);
        $(`#sms${typeCapitalized}`).prop('checked', data.sms_enabled);
        $(`#push${typeCapitalized}`).prop('checked', data.push_enabled);
        $(`#inApp${typeCapitalized}`).prop('checked', data.in_app_enabled);

        // Set priority filter
        $(`.priority-filter[data-type="${type}"]`).val(data.priority_filter || '');

        // Set auto-mark read
        $(`#autoMarkRead${typeCapitalized}`).prop('checked', data.auto_mark_read);

        // Set master toggle based on whether any channel is enabled
        const anyEnabled = data.email_enabled || data.sms_enabled || data.push_enabled || data.in_app_enabled;
        $(`#masterToggle${typeCapitalized}`).prop('checked', anyEnabled);

        // Trigger master toggle to set card state
        $(`#masterToggle${typeCapitalized}`).trigger('change');
    }

    function savePreferences() {
        const preferences = [];

        $('.notification-type-card').each(function() {
            const type = $(this).data('type');
            const typeCapitalized = type.charAt(0).toUpperCase() + type.slice(1);

            preferences.push({
                notification_type: type,
                email_enabled: $(`#email${typeCapitalized}`).is(':checked'),
                sms_enabled: $(`#sms${typeCapitalized}`).is(':checked'),
                push_enabled: $(`#push${typeCapitalized}`).is(':checked'),
                in_app_enabled: $(`#inApp${typeCapitalized}`).is(':checked'),
                sound_enabled: true, // Always enabled for now
                priority_filter: $(`.priority-filter[data-type="${type}"]`).val() || null,
                quiet_hours_start: null, // Global setting for now
                quiet_hours_end: null, // Global setting for now
                weekend_notifications: true, // Global setting for now
                digest_frequency: 'daily', // Global setting for now
                auto_mark_read: $(`#autoMarkRead${typeCapitalized}`).is(':checked')
            });
        });

        $.ajax({
            url: '{{ route("owner.notification-preferences.update") }}',
            type: 'POST',
            data: { preferences: preferences },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                } else {
                    toastr.error('Failed to save preferences');
                }
            },
            error: function(xhr) {
                toastr.error('Failed to save preferences');
                console.error('Save preferences error:', xhr);
            }
        });
    }

    function resetPreferences() {
        $.ajax({
            url: '{{ route("owner.notification-preferences.reset") }}',
            type: 'POST',
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    location.reload();
                } else {
                    toastr.error('Failed to reset preferences');
                }
            },
            error: function(xhr) {
                toastr.error('Failed to reset preferences');
                console.error('Reset preferences error:', xhr);
            }
        });
    }

    function sendTestNotification() {
        const type = $('#testNotificationType').val();
        const channels = [];

        $('#testNotificationModal input[type="checkbox"]:checked').each(function() {
            channels.push($(this).val());
        });

        if (channels.length === 0) {
            toastr.warning('Please select at least one channel to test');
            return;
        }

        $.ajax({
            url: '{{ route("owner.notification-preferences.test") }}',
            type: 'POST',
            data: {
                notification_type: type,
                channels: channels
            },
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message);
                    $('#testNotificationModal').modal('hide');
                } else {
                    toastr.error('Failed to send test notification');
                }
            },
            error: function(xhr) {
                toastr.error('Failed to send test notification');
                console.error('Test notification error:', xhr);
            }
        });
    }
});

// Helper function to get notification type icon
function getTypeIcon(type) {
    const icons = {
        'booking': 'fas fa-calendar-check',
        'cancellation': 'fas fa-calendar-times',
        'payment': 'fas fa-credit-card',
        'review': 'fas fa-star',
        'system': 'fas fa-cog',
        'marketing': 'fas fa-bullhorn',
        'alert': 'fas fa-exclamation-triangle',
        'reminder': 'fas fa-clock',
        'customer_message': 'fas fa-comment',
        'waiting_list': 'fas fa-list'
    };
    return icons[type] || 'fas fa-bell';
}
</script>
@stop
